import { Editor } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'

// Alpine.js component for rich text editor
window.richTextEditor = function(config) {
    return {
        editor: null,
        content: config.value || '',
        name: config.name || '',
        placeholder: config.placeholder || 'Start typing...',
        disabled: config.disabled || false,
        wireModel: config.wireModel || null,

        // Reactive states for toolbar buttons
        isBold: false,
        isItalic: false,
        isUnderline: false,
        isBulletList: false,
        isOrderedList: false,
        isLink: false,
        canUndo: false,
        canRedo: false,

        initEditor() {
            this.$nextTick(() => {
                this.editor = new Editor({
                    element: this.$refs.editorElement,
                    extensions: [
                        StarterKit,
                        Underline,
                        Link.configure({
                            openOnClick: false,
                            HTMLAttributes: {
                                class: 'text-blue-600 underline',
                            },
                        }),
                        Placeholder.configure({
                            placeholder: this.placeholder,
                        }),
                    ],
                    content: this.content,
                    editable: !this.disabled,
                    onUpdate: ({ editor }) => {
                        this.content = editor.getHTML();
                        this.updateToolbarStates();
                    },
                    onSelectionUpdate: ({ editor }) => {
                        this.updateToolbarStates();
                    },
                });

                // Watch for external content changes
                this.$watch('content', (newContent) => {
                    if (this.editor && this.editor.getHTML() !== newContent) {
                        this.editor.commands.setContent(newContent, false);
                    }
                });

                // Watch for disabled state changes
                this.$watch('disabled', (newDisabled) => {
                    if (this.editor) {
                        this.editor.setEditable(!newDisabled);
                    }
                });
            });
        },

        updateToolbarStates() {
            if (!this.editor) return;

            this.isBold = this.editor.isActive('bold');
            this.isItalic = this.editor.isActive('italic');
            this.isUnderline = this.editor.isActive('underline');
            this.isBulletList = this.editor.isActive('bulletList');
            this.isOrderedList = this.editor.isActive('orderedList');
            this.isLink = this.editor.isActive('link');
            this.canUndo = this.editor.can().undo();
            this.canRedo = this.editor.can().redo();
        },

        toggleBold() {
            this.editor.chain().focus().toggleBold().run();
        },

        toggleItalic() {
            this.editor.chain().focus().toggleItalic().run();
        },

        toggleUnderline() {
            this.editor.chain().focus().toggleUnderline().run();
        },

        toggleBulletList() {
            this.editor.chain().focus().toggleBulletList().run();
        },

        toggleOrderedList() {
            this.editor.chain().focus().toggleOrderedList().run();
        },

        toggleLink() {
            if (this.editor.isActive('link')) {
                this.editor.chain().focus().unsetLink().run();
            } else {
                const url = window.prompt('Enter URL:');
                if (url) {
                    this.editor.chain().focus().setLink({ href: url }).run();
                }
            }
        },

        undo() {
            this.editor.chain().focus().undo().run();
        },

        redo() {
            this.editor.chain().focus().redo().run();
        },

        destroy() {
            if (this.editor) {
                this.editor.destroy();
            }
        }
    }
}
