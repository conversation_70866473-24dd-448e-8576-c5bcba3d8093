import { Editor } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'

// Alpine.js component for rich text editor
window.richTextEditor = function(config) {
    return {
        editor: null,
        content: config.value || '',
        name: config.name || '',
        placeholder: config.placeholder || 'Start typing...',
        disabled: config.disabled || false,
        wireModel: config.wireModel || null,

        // Reactive states for toolbar buttons
        isBold: false,
        isItalic: false,
        isUnderline: false,
        isBulletList: false,
        isOrderedList: false,
        isLink: false,
        canUndo: false,
        canRedo: false,

        initEditor() {
            // Add a small delay to ensure DOM is ready
            setTimeout(() => {
                this.$nextTick(() => {
                    if (!this.$refs.editorElement) {
                        console.error('Editor element not found');
                        return;
                    }

                    try {
                        this.editor = new Editor({
                            element: this.$refs.editorElement,
                            extensions: [
                                StarterKit,
                                Underline,
                                Link.configure({
                                    openOnClick: false,
                                    HTMLAttributes: {
                                        class: 'text-blue-600 underline',
                                    },
                                }),
                                Placeholder.configure({
                                    placeholder: this.placeholder,
                                }),
                            ],
                            content: this.content,
                            editable: !this.disabled,
                            onUpdate: ({ editor }) => {
                                this.content = editor.getHTML();
                                this.updateToolbarStates();
                            },
                            onSelectionUpdate: ({ editor }) => {
                                this.updateToolbarStates();
                            },
                            onCreate: ({ editor }) => {
                                this.updateToolbarStates();
                            },
                        });

                        // Watch for external content changes
                        this.$watch('content', (newContent) => {
                            if (this.editor && this.editor.getHTML() !== newContent) {
                                this.editor.commands.setContent(newContent, false);
                            }
                        });

                        // Watch for disabled state changes
                        this.$watch('disabled', (newDisabled) => {
                            if (this.editor) {
                                this.editor.setEditable(!newDisabled);
                            }
                        });
                    } catch (error) {
                        console.error('Failed to initialize TipTap editor:', error);
                    }
                });
            }, 50);
        },

        updateToolbarStates() {
            if (!this.editor || !this.editor.view || this.editor.isDestroyed) return;

            try {
                this.isBold = this.editor.isActive('bold');
                this.isItalic = this.editor.isActive('italic');
                this.isUnderline = this.editor.isActive('underline');
                this.isBulletList = this.editor.isActive('bulletList');
                this.isOrderedList = this.editor.isActive('orderedList');
                this.isLink = this.editor.isActive('link');
                this.canUndo = this.editor.can().undo();
                this.canRedo = this.editor.can().redo();
            } catch (error) {
                console.error('Error updating toolbar states:', error);
            }
        },

        toggleBold() {
            if (!this.editor || !this.editor.view || this.editor.isDestroyed) {
                console.warn('Editor not ready for toggleBold');
                return;
            }
            try {
                this.editor.chain().focus().toggleBold().run();
            } catch (error) {
                console.error('Error in toggleBold:', error);
            }
        },

        toggleItalic() {
            if (!this.editor || !this.editor.view || this.editor.isDestroyed) {
                console.warn('Editor not ready for toggleItalic');
                return;
            }
            try {
                this.editor.chain().focus().toggleItalic().run();
            } catch (error) {
                console.error('Error in toggleItalic:', error);
            }
        },

        toggleUnderline() {
            if (!this.editor || !this.editor.view || this.editor.isDestroyed) {
                console.warn('Editor not ready for toggleUnderline');
                return;
            }
            try {
                this.editor.chain().focus().toggleUnderline().run();
            } catch (error) {
                console.error('Error in toggleUnderline:', error);
            }
        },

        toggleBulletList() {
            if (!this.editor || !this.editor.view || this.editor.isDestroyed) {
                console.warn('Editor not ready for toggleBulletList');
                return;
            }
            try {
                this.editor.chain().focus().toggleBulletList().run();
            } catch (error) {
                console.error('Error in toggleBulletList:', error);
            }
        },

        toggleOrderedList() {
            if (!this.editor || !this.editor.view || this.editor.isDestroyed) {
                console.warn('Editor not ready for toggleOrderedList');
                return;
            }
            try {
                this.editor.chain().focus().toggleOrderedList().run();
            } catch (error) {
                console.error('Error in toggleOrderedList:', error);
            }
        },

        toggleLink() {
            if (!this.editor || !this.editor.view || this.editor.isDestroyed) {
                console.warn('Editor not ready for toggleLink');
                return;
            }
            try {
                if (this.editor.isActive('link')) {
                    this.editor.chain().focus().unsetLink().run();
                } else {
                    const url = window.prompt('Enter URL:');
                    if (url) {
                        this.editor.chain().focus().setLink({ href: url }).run();
                    }
                }
            } catch (error) {
                console.error('Error in toggleLink:', error);
            }
        },

        undo() {
            if (!this.editor || !this.editor.view || this.editor.isDestroyed) {
                console.warn('Editor not ready for undo');
                return;
            }
            try {
                this.editor.chain().focus().undo().run();
            } catch (error) {
                console.error('Error in undo:', error);
            }
        },

        redo() {
            if (!this.editor || !this.editor.view || this.editor.isDestroyed) {
                console.warn('Editor not ready for redo');
                return;
            }
            try {
                this.editor.chain().focus().redo().run();
            } catch (error) {
                console.error('Error in redo:', error);
            }
        },

        destroy() {
            if (this.editor) {
                this.editor.destroy();
            }
        }
    }
}
